#!/usr/bin/env python3

import binascii

# 从你的日志中提取的合并数据包
raw_hex = "02FD80020000000700010E80003E8002FD8001000000000E00010E80620122888E4966A852272"

print(f"原始数据: {raw_hex}")
print(f"数据长度: {len(raw_hex)//2} 字节")

try:
    raw_data = binascii.a2b_hex(raw_hex)
    
    # 手动分析第一个包
    print("\n=== 第一个包分析 ===")
    print("前8字节(头部):", binascii.b2a_hex(raw_data[:8]).decode().upper())
    payload_len1 = int.from_bytes(raw_data[4:8], byteorder='big')
    print(f"Payload长度: {payload_len1}")
    packet1_len = 8 + payload_len1
    print(f"第一个包总长度: {packet1_len}")
    
    packet1_data = raw_data[:packet1_len]
    print(f"第一个包数据: {binascii.b2a_hex(packet1_data).decode().upper()}")
    
    # 手动分析第二个包
    print("\n=== 第二个包分析 ===")
    offset = packet1_len
    print(f"第二个包开始位置: {offset}")
    print("第二个包头部:", binascii.b2a_hex(raw_data[offset:offset+8]).decode().upper())
    payload_len2 = int.from_bytes(raw_data[offset+4:offset+8], byteorder='big')
    print(f"Payload长度: {payload_len2}")
    packet2_len = 8 + payload_len2
    print(f"第二个包总长度: {packet2_len}")
    
    packet2_data = raw_data[offset:offset+packet2_len]
    print(f"第二个包数据: {binascii.b2a_hex(packet2_data).decode().upper()}")
    
    print(f"\n总计: {packet1_len + packet2_len} 字节，原始数据: {len(raw_data)} 字节")
    
except Exception as e:
    print(f"分析失败: {e}")
