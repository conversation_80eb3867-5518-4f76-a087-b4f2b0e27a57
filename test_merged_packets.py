#!/usr/bin/env python3
"""
测试合并DoIP数据包的分离和处理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scapy.contrib.automotive.doip import DoIP
from scapy.contrib.automotive.uds import UDS
import binascii

def test_packet_splitting():
    """测试数据包分离功能"""
    print("=== 测试合并数据包分离 ===")
    
    # 你提供的合并数据包（从日志中提取，去掉空格）
    # 原始: 02 FD 80 02 00 00 00 07 00 01 0E 80 00 3E 80 02 FD 80 01 00 00 00 0E 00 01 0E 80 62 01 C2 88 E4 96 6A 85 22 72
    merged_hex = "02FD80020000000700010E80003E8002FD8001000000000E00010E80620122888E4966A852272"
    
    print(f"原始合并数据: {merged_hex}")
    print(f"数据长度: {len(merged_hex)//2} 字节")
    
    try:
        merged_data = binascii.a2b_hex(merged_hex)
        
        # 手动分离数据包
        packets = []
        offset = 0
        
        while offset < len(merged_data):
            if offset + 8 > len(merged_data):
                break
                
            # 解析DoIP头部
            payload_length = int.from_bytes(merged_data[offset+4:offset+8], byteorder='big')
            packet_length = 8 + payload_length
            
            if offset + packet_length > len(merged_data):
                break
                
            packet_data = merged_data[offset:offset + packet_length]
            packet = DoIP(packet_data)
            packets.append(packet)
            
            print(f"\n数据包 {len(packets)}:")
            print(f"  原始数据: {binascii.b2a_hex(packet_data).decode().upper()}")
            print(f"  payload_type: 0x{packet.payload_type:04x}")
            print(f"  payload_length: {payload_length}")
            
            if packet.haslayer('UDS'):
                service = packet[UDS].service
                print(f"  UDS service: 0x{service:02x}")
                
                if service == 0x62:
                    print(f"  -> 这是DID读取的正响应")
                elif service == 0x7f:
                    print(f"  -> 这是负响应")
            else:
                print(f"  -> 这是ACK响应（无UDS层）")
            
            offset += packet_length
        
        print(f"\n✅ 成功分离出 {len(packets)} 个数据包")
        
        # 验证分离结果
        expected_packets = [
            {"payload_type": 0x8002, "description": "3e80的ACK响应"},
            {"payload_type": 0x8001, "description": "DID的正常响应", "uds_service": 0x62}
        ]
        
        if len(packets) == len(expected_packets):
            print("✅ 数据包数量正确")
            
            for i, (packet, expected) in enumerate(zip(packets, expected_packets)):
                if packet.payload_type == expected["payload_type"]:
                    print(f"✅ 数据包{i+1} payload_type正确")
                    
                    if "uds_service" in expected:
                        if packet.haslayer('UDS') and packet[UDS].service == expected["uds_service"]:
                            print(f"✅ 数据包{i+1} UDS服务正确")
                        else:
                            print(f"❌ 数据包{i+1} UDS服务错误")
                            return False
                else:
                    print(f"❌ 数据包{i+1} payload_type错误")
                    return False
        else:
            print(f"❌ 数据包数量错误: 期望{len(expected_packets)}, 实际{len(packets)}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 数据包分离失败: {e}")
        return False

def test_response_selection():
    """测试响应选择逻辑"""
    print("\n=== 测试响应选择逻辑 ===")
    
    test_cases = [
        {
            "name": "等待DID响应时收到合并包",
            "current_service": 0x22,
            "packets": [
                {"payload_type": 0x8002, "description": "3e80 ACK"},
                {"payload_type": 0x8001, "uds_service": 0x62, "description": "DID正响应"}
            ],
            "expected_selection": 1,  # 选择第二个包
            "expected_description": "DID正响应"
        },
        {
            "name": "等待3e80响应时收到合并包",
            "current_service": 0x3e,
            "packets": [
                {"payload_type": 0x8002, "description": "3e80 ACK"},
                {"payload_type": 0x8001, "uds_service": 0x62, "description": "DID正响应"}
            ],
            "expected_selection": 0,  # 选择第一个包
            "expected_description": "3e80 ACK"
        },
        {
            "name": "等待DID响应时收到NRC",
            "current_service": 0x22,
            "packets": [
                {"payload_type": 0x8002, "description": "3e80 ACK"},
                {"payload_type": 0x8001, "uds_service": 0x7f, "description": "NRC响应"}
            ],
            "expected_selection": 1,  # 选择第二个包
            "expected_description": "NRC响应"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试用例: {case['name']}")
        print(f"  当前等待服务: 0x{case['current_service']:02x}")
        print(f"  数据包情况:")
        
        for i, packet_info in enumerate(case['packets']):
            print(f"    {i+1}. {packet_info['description']} (payload_type: 0x{packet_info['payload_type']:04x})")
        
        # 模拟选择逻辑
        selected_index = None
        my_service = case['current_service']
        
        for i, packet_info in enumerate(case['packets']):
            if packet_info['payload_type'] == 0x8002:
                if my_service == 0x3e:
                    selected_index = i
                    break
                else:
                    continue  # 跳过ACK
            else:
                if 'uds_service' in packet_info:
                    expected_service = my_service | 0x40
                    nrc_service = 0x7f
                    actual_service = packet_info['uds_service']
                    
                    if actual_service == expected_service or actual_service == nrc_service:
                        selected_index = i
                        break
                else:
                    selected_index = i
                    break
        
        print(f"  期望选择: 数据包{case['expected_selection']+1} ({case['expected_description']})")
        print(f"  实际选择: 数据包{selected_index+1 if selected_index is not None else 'None'}")
        
        if selected_index == case['expected_selection']:
            print("  ✅ 选择正确")
        else:
            print("  ❌ 选择错误")
            return False
    
    return True

def main():
    print("开始测试合并DoIP数据包处理...")
    print("=" * 60)
    
    tests = [
        ("数据包分离功能", test_packet_splitting),
        ("响应选择逻辑", test_response_selection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！合并数据包处理应该能正常工作。")
        print("\n修复要点:")
        print("1. 新增split_merged_doip_packets函数分离合并的DoIP数据包")
        print("2. process_response函数支持合并数据包的自动分离")
        print("3. my_receiver函数智能选择目标响应，跳过混入的ACK")
        print("4. 根据当前等待的服务类型选择合适的响应数据包")
        return True
    else:
        print("\n⚠️  部分测试失败，需要进一步检查修复。")
        return False

if __name__ == "__main__":
    main()
