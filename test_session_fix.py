#!/usr/bin/env python3
"""
测试脚本：验证会话维持期间ACK响应处理的修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scapy.contrib.automotive.doip import DoIP
from scapy.contrib.automotive.uds import UDS, UDS_NR
import binascii

def test_ack_response_parsing():
    """测试ACK响应的解析"""
    print("测试ACK响应解析...")
    
    # 模拟ACK响应: 02fd80020000000700010e80003e80
    ack_data = "02fd80020000000700010e80003e80"
    try:
        ack_packet = DoIP(binascii.a2b_hex(ack_data))
        print(f"ACK包解析成功:")
        print(f"  payload_type: 0x{ack_packet.payload_type:04x}")
        print(f"  source_address: 0x{ack_packet.source_address:04x}")
        print(f"  target_address: 0x{ack_packet.target_address:04x}")
        
        # 检查是否有UDS层
        if ack_packet.haslayer('UDS'):
            print(f"  包含UDS层")
        else:
            print(f"  不包含UDS层 (这是正常的ACK响应)")
            
        return True
    except Exception as e:
        print(f"ACK包解析失败: {e}")
        return False

def test_normal_3e80_response():
    """测试正常的3e80响应"""
    print("\n测试正常3e80响应解析...")
    
    # 模拟正常3e80响应: 02fd80010000000300007e80
    normal_data = "02fd80010000000300007e80"
    try:
        normal_packet = DoIP(binascii.a2b_hex(normal_data))
        print(f"正常3e80响应解析成功:")
        print(f"  payload_type: 0x{normal_packet.payload_type:04x}")
        print(f"  source_address: 0x{normal_packet.source_address:04x}")
        print(f"  target_address: 0x{normal_packet.target_address:04x}")
        
        if normal_packet.haslayer('UDS'):
            print(f"  UDS service: 0x{normal_packet[UDS].service:02x}")
        
        return True
    except Exception as e:
        print(f"正常3e80响应解析失败: {e}")
        return False

def test_did_response():
    """测试DID响应"""
    print("\n测试DID响应解析...")
    
    # 模拟DID响应: 02fd80010000000700006201234567
    did_data = "02fd80010000000700006201234567"
    try:
        did_packet = DoIP(binascii.a2b_hex(did_data))
        print(f"DID响应解析成功:")
        print(f"  payload_type: 0x{did_packet.payload_type:04x}")
        
        if did_packet.haslayer('UDS'):
            print(f"  UDS service: 0x{did_packet[UDS].service:02x}")
            if did_packet[UDS].service == 0x62:
                print(f"  这是一个有效的DID读取响应")
        
        return True
    except Exception as e:
        print(f"DID响应解析失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试会话维持ACK响应处理修复...")
    
    success_count = 0
    total_tests = 3
    
    if test_ack_response_parsing():
        success_count += 1
    
    if test_normal_3e80_response():
        success_count += 1
        
    if test_did_response():
        success_count += 1
    
    print(f"\n测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！修复应该能正常工作。")
    else:
        print("❌ 部分测试失败，需要进一步检查。")
