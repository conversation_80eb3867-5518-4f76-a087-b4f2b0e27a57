from scapy.contrib.automotive.doip import DoIP
from scapy.contrib.automotive.uds import UDS, UDS_NR
import binascii
from scapy.layers.inet import *
import socket as sc
import time
RED = "\033[1;31m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
WHITE = "\033[1;37m"
YELLOW = "\033[1;33m"
GREEN = "\033[1;32m"
RESET = "\033[1;0m"

payload_types = [0x0000,0x0001,0x0002,0x0003,0x0004,0x0005,0x0006,0x0007,0x0008,0x4001,0x4002,0x4003,0x4004,0x8001,0x8003]


def int_to_hexstr(num, length=2):
    return '{:0{length}x}'.format(num, length=length)   


def receiver(socket, timeout=2):
    resp = None
    socket.settimeout(timeout)
    try:
        resp = DoIP(socket.recv(2048))
        while resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
            resp = DoIP(socket.recv(2048))
    except sc.timeout:
        pass
    finally:
        return resp

def split_merged_doip_packets(raw_data):
    """
    分离合并的DoIP数据包
    返回DoIP数据包列表
    """
    packets = []
    offset = 0

    while offset < len(raw_data):
        try:
            # DoIP头部至少8字节
            if offset + 8 > len(raw_data):
                break

            # 解析DoIP头部获取payload长度
            # DoIP格式: version(1) + inverse_version(1) + payload_type(2) + payload_length(4)
            payload_length = int.from_bytes(raw_data[offset+4:offset+8], byteorder='big')
            packet_length = 8 + payload_length  # 头部8字节 + payload长度

            if offset + packet_length > len(raw_data):
                break

            # 提取单个DoIP数据包
            packet_data = raw_data[offset:offset + packet_length]
            packets.append(DoIP(packet_data))

            offset += packet_length

        except Exception as e:
            # 如果解析失败，尝试作为单个包处理
            try:
                packets.append(DoIP(raw_data[offset:]))
            except:
                pass
            break

    return packets

def process_response(resp, logger):
    """
    处理响应数据，支持合并数据包的分离
    """
    try:
        # 尝试分离合并的DoIP数据包
        packets = split_merged_doip_packets(resp)

        if len(packets) == 1:
            # 单个数据包，直接返回
            packet = packets[0]
            my_logger(packet, 0, logger)
            return packet
        elif len(packets) > 1:
            # 多个数据包，记录所有包但只返回最后一个非ACK包
            print(f"[DEBUG] 检测到{len(packets)}个合并的DoIP数据包")

            target_packet = None
            for i, packet in enumerate(packets):
                my_logger(packet, 0, logger)
                print(f"[DEBUG] 数据包{i+1}: payload_type=0x{packet.payload_type:04x}")

                # 优先选择非ACK响应(payload_type != 0x8002)
                if packet.payload_type != 0x8002:
                    target_packet = packet
                elif target_packet is None:
                    # 如果没有非ACK包，使用第一个ACK包
                    target_packet = packet

            return target_packet if target_packet else packets[-1]
        else:
            # 解析失败，使用原始逻辑
            return DoIP(resp)

    except Exception as e:
        # 如果新逻辑失败，回退到原始逻辑
        print(f"[DEBUG] 数据包分离失败，使用原始逻辑: {e}")
        try:
            packet = DoIP(resp)
            my_logger(packet, 0, logger)
            return packet
        except:
            return None

def deal_8002_data(resp, logger, sendData, recvTime, socket, first = True):
    while resp and resp[DoIP].payload_type == 0x8002:
        if recvTime:
            time.sleep(recvTime)
        previous_msg = binascii.b2a_hex(resp[DoIP].previous_msg).decode('utf-8')
        previous_sendData = binascii.b2a_hex(raw(sendData[UDS])).decode('utf-8')

        if previous_msg[:len(previous_sendData)] == previous_sendData:
            previous_msg = previous_msg[len(previous_sendData):]

        if len(previous_msg) > 0 and '02fd8001' == previous_msg[:8]:
            resp = DoIP(binascii.a2b_hex(previous_msg))
            my_logger(resp, 0, logger)
            x = binascii.b2a_hex(raw(resp)).decode('utf-8')
            kk = previous_msg.split(x)
            if len(kk) > 1 and len(kk[-1]) > 0:
                resp = DoIP(binascii.a2b_hex(kk[-1]))
                my_logger(resp, 0, logger)
        else:
            if first:
                resp = process_response(socket.recv(2048), logger)
            else:
                break
    return resp

def deal_nrc78_data(socket, logger, pstarTimeout, doip, sendData, recvTime):
    resp = None
    startTime = time.time()
    lastSendTime = startTime
    sendInterval = 1.5

    while True:
        try:
            resp = socket.recv(2048)
            if resp:
                resp = process_response(resp,logger)
                if resp and resp[DoIP].payload_type == 0x8002:
                    resp = deal_8002_data(resp, logger, sendData, recvTime, socket, first = False)
                if resp and resp[DoIP].payload_type == 0x8001 and resp.haslayer('UDS') and (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
                    startTime = time.time()
                if resp and resp[DoIP].payload_type != 0x8002 and (resp[DoIP].payload_type == 0x8001 and resp.haslayer('UDS') and ( resp[UDS].service != 0x7f or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode != 0x78))):
                    break
        except sc.timeout:
            currentTime = time.time()
            if currentTime - startTime > pstarTimeout:
                break
            elif currentTime - startTime <= pstarTimeout:
                if currentTime - lastSendTime > sendInterval:
                    socket.sendall(raw(doip / UDS(binascii.a2b_hex('3e80'))))
                    my_logger(doip / UDS(binascii.a2b_hex('3e80')), 1, logger)
                    sendData = doip / UDS(binascii.a2b_hex('3e80'))
                    lastSendTime = currentTime
    return resp

def my_receiver(socket, timeout, pstarTimeout, logger, my_service, recvTime, doip, sendData):
    resp = None
    privious_resp = None

    socket.settimeout(timeout)
    try:
        raw_data = socket.recv(2048)

        # 检查是否有合并的数据包
        packets = split_merged_doip_packets(raw_data)

        if len(packets) > 1:
            print(f"[DEBUG] 检测到{len(packets)}个合并的DoIP数据包，正在分离...")

            # 记录所有数据包
            for i, packet in enumerate(packets):
                my_logger(packet, 0, logger)
                print(f"[DEBUG] 数据包{i+1}: payload_type=0x{packet.payload_type:04x}")

            # 查找目标响应（非3e80 ACK的响应）
            target_resp = None
            for packet in packets:
                if packet.payload_type == 0x8002:
                    # 检查是否是3e80的ACK
                    if my_service == 0x3e:
                        # 如果正在等待3e80响应，这个ACK就是目标
                        target_resp = packet
                        break
                    else:
                        # 如果不是等待3e80响应，跳过这个ACK
                        print(f"[DEBUG] 跳过3e80 ACK响应，继续寻找服务0x{my_service:02x}的响应...")
                        continue
                else:
                    # 非ACK响应，可能是目标响应
                    if packet.haslayer('UDS'):
                        expected_service = my_service | 0x40  # 正响应
                        nrc_service = 0x7f  # 负响应
                        actual_service = packet[UDS].service

                        if actual_service == expected_service or actual_service == nrc_service:
                            target_resp = packet
                            print(f"[DEBUG] 找到目标响应: 服务0x{actual_service:02x}")
                            break
                    else:
                        # 没有UDS层的响应也可能是目标
                        target_resp = packet

            if target_resp:
                resp = target_resp
            else:
                # 如果没找到目标响应，使用最后一个非ACK包
                for packet in reversed(packets):
                    if packet.payload_type != 0x8002:
                        resp = packet
                        break
                if not resp:
                    resp = packets[-1]  # 最后的选择
        else:
            # 单个数据包，正常处理
            resp = process_response(raw_data, logger)

        # 继续原有的处理逻辑
        while True:
            if resp and resp[DoIP].payload_type == 0x8002:
                if my_service == 0x3e:
                    return '3e80_ack'
                else:
                    # 对于非3e80请求收到的ACK，已经在上面处理过了
                    # 如果到这里说明没有找到合适的响应，继续接收
                    print(f"[DEBUG] 未找到服务0x{my_service:02x}的响应，继续接收...")
                    resp = process_response(socket.recv(2048), logger)
                    continue
            elif resp and resp.haslayer('UDS') and (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
                privious_resp = resp
                resp = deal_nrc78_data(socket, logger, pstarTimeout, doip, sendData, recvTime)
            else:
                if resp is None and privious_resp is not None:
                    resp = privious_resp
                break
    except sc.timeout:
        pass
    finally:
        if resp and resp[DoIP].payload_type == 0x8001 and not resp.haslayer('UDS'):
            print(RED + 'Has not get UDS data, use set --delay' + RESET)
            return 'delay_error'
        return resp

def my_logger(data, send, logger):
    if hasattr(data,'source_address') and data[DoIP].source_address is not None:
        tx = data[DoIP].source_address
        data = binascii.b2a_hex(raw(data)).decode('utf-8').upper()
        data = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
        if send:
            logger.info('Tx【'+int_to_hexstr(tx,4)+'】--> '+data)
        else:
            logger.info('Rx【'+int_to_hexstr(tx,4)+'】<-- '+data)
    else:
        data = binascii.b2a_hex(raw(data)).decode('utf-8').upper()
        data = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
        if send:
            logger.info('Sender   --> '+data)
        else:
            logger.info('Receiver <-- '+data)