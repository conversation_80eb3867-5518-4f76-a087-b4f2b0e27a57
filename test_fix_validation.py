#!/usr/bin/env python3
"""
验证会话维持ACK响应处理修复的测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scapy.contrib.automotive.doip import DoIP
from scapy.contrib.automotive.uds import UDS
import binascii

def test_ack_response_handling():
    """测试ACK响应处理逻辑"""
    print("=== 测试ACK响应处理逻辑 ===")
    
    # 模拟ACK响应数据: 02fd80020000000700010e80003e80
    ack_hex = "02fd80020000000700010e80003e80"
    
    try:
        # 解析ACK响应
        ack_packet = DoIP(binascii.a2b_hex(ack_hex))
        
        print(f"✅ ACK包解析成功:")
        print(f"   - payload_type: 0x{ack_packet.payload_type:04x} (应该是0x8002)")
        print(f"   - source_address: 0x{ack_packet.source_address:04x}")
        print(f"   - target_address: 0x{ack_packet.target_address:04x}")
        
        # 检查payload_type是否为0x8002
        if ack_packet.payload_type == 0x8002:
            print("✅ 正确识别为ACK响应 (payload_type = 0x8002)")
        else:
            print(f"❌ payload_type错误: 期望0x8002, 实际0x{ack_packet.payload_type:04x}")
            return False
            
        # 检查是否包含UDS层
        if ack_packet.haslayer('UDS'):
            print("⚠️  ACK响应包含UDS层 (这可能会导致问题)")
        else:
            print("✅ ACK响应不包含UDS层 (这是正确的)")
            
        return True
        
    except Exception as e:
        print(f"❌ ACK包解析失败: {e}")
        return False

def test_normal_3e80_response():
    """测试正常3e80响应"""
    print("\n=== 测试正常3e80响应 ===")
    
    # 模拟正常3e80响应: 02fd80010000000300007e80
    normal_hex = "02fd80010000000300007e80"
    
    try:
        normal_packet = DoIP(binascii.a2b_hex(normal_hex))
        
        print(f"✅ 正常3e80响应解析成功:")
        print(f"   - payload_type: 0x{normal_packet.payload_type:04x} (应该是0x8001)")
        print(f"   - source_address: 0x{normal_packet.source_address:04x}")
        print(f"   - target_address: 0x{normal_packet.target_address:04x}")
        
        if normal_packet.payload_type == 0x8001:
            print("✅ 正确识别为正常DoIP响应 (payload_type = 0x8001)")
        else:
            print(f"❌ payload_type错误: 期望0x8001, 实际0x{normal_packet.payload_type:04x}")
            return False
            
        if normal_packet.haslayer('UDS'):
            uds_service = normal_packet[UDS].service
            print(f"✅ 包含UDS层, service: 0x{uds_service:02x} (应该是0x7e)")
            if uds_service == 0x7e:
                print("✅ 正确的3e80响应服务码")
            else:
                print(f"❌ 服务码错误: 期望0x7e, 实际0x{uds_service:02x}")
                return False
        else:
            print("❌ 正常3e80响应应该包含UDS层")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 正常3e80响应解析失败: {e}")
        return False

def test_did_response():
    """测试DID响应"""
    print("\n=== 测试DID响应 ===")
    
    # 模拟DID响应: 02fd80010000000700006201234567
    did_hex = "02fd80010000000700006201234567"
    
    try:
        did_packet = DoIP(binascii.a2b_hex(did_hex))
        
        print(f"✅ DID响应解析成功:")
        print(f"   - payload_type: 0x{did_packet.payload_type:04x} (应该是0x8001)")
        
        if did_packet.payload_type == 0x8001:
            print("✅ 正确识别为正常DoIP响应")
        else:
            print(f"❌ payload_type错误: 期望0x8001, 实际0x{did_packet.payload_type:04x}")
            return False
            
        if did_packet.haslayer('UDS'):
            uds_service = did_packet[UDS].service
            print(f"✅ 包含UDS层, service: 0x{uds_service:02x} (应该是0x62)")
            if uds_service == 0x62:
                print("✅ 正确的DID读取响应服务码")
            else:
                print(f"❌ 服务码错误: 期望0x62, 实际0x{uds_service:02x}")
                return False
        else:
            print("❌ DID响应应该包含UDS层")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ DID响应解析失败: {e}")
        return False

def main():
    print("开始验证会话维持ACK响应处理修复...")
    print("=" * 50)
    
    tests = [
        ("ACK响应处理", test_ack_response_handling),
        ("正常3e80响应", test_normal_3e80_response),
        ("DID响应", test_did_response)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复应该能正常工作。")
        print("\n修复说明:")
        print("1. my_receiver函数现在能正确处理3e80请求的ACK响应")
        print("2. 当service=0x3e且收到0x8002类型响应时，直接返回而不报错")
        print("3. dumpdids等功能现在会检查响应是否包含UDS层")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查修复。")
        return False

if __name__ == "__main__":
    main()
